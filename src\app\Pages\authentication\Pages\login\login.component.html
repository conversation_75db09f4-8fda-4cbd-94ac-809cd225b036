<div class="login-card card shadow-sm">
  <div class="card-body">
    <div class="login-header text-center mb-4">
      <h1 class="login-title h3 mb-2">Welcome Back</h1>
      <p class="login-subtitle text-muted">Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- Email Field -->
      <div class="auth-input-container mb-3">
        <div class="auth-input-wrapper position-relative">
          <span class="p-float-label">
            <input
              type="email"
              id="email"
              formControlName="email"
              placeholder="Email"
              autocomplete="email"
              pInputText
              class="form-control"
              [class.is-invalid]="
                emailControl?.invalid && emailControl?.touched
              "
            />
            <label for="email">Email</label>
          </span>
          <i class="pi pi-envelope auth-input-icon position-absolute"></i>
        </div>
        <div
          *ngIf="emailControl?.hasError('required') && emailControl?.touched"
          class="auth-error-message text-danger d-flex align-items-center mt-1"
        >
          <i class="pi pi-exclamation-triangle me-1"></i>
          <small>Email is required</small>
        </div>
        <div
          *ngIf="emailControl?.hasError('email') && emailControl?.touched"
          class="auth-error-message text-danger d-flex align-items-center mt-1"
        >
          <i class="pi pi-exclamation-triangle me-1"></i>
          <small>Please enter a valid email address</small>
        </div>
      </div>

      <!-- Password Field -->
      <div class="auth-input-container mb-3">
        <div class="auth-input-wrapper position-relative">
          <span class="p-float-label">
            <p-password
              formControlName="password"
              placeholder="Password"
              autocomplete="current-password"
              inputStyleClass="form-control"
              [class.is-invalid]="
                passwordControl?.invalid && passwordControl?.touched
              "
            ></p-password>
            <label for="password">Password</label>
          </span>
          <i class="pi pi-lock auth-input-icon position-absolute"></i>
        </div>
        <div
          *ngIf="
            passwordControl?.hasError('required') && passwordControl?.touched
          "
          class="auth-error-message text-danger d-flex align-items-center mt-1"
        >
          <i class="pi pi-exclamation-triangle me-1"></i>
          <small>Password is required</small>
        </div>
        <div
          *ngIf="
            passwordControl?.hasError('minlength') && passwordControl?.touched
          "
          class="auth-error-message text-danger d-flex align-items-center mt-1"
        >
          <i class="pi pi-exclamation-triangle me-1"></i>
          <small>Password must be at least 6 characters long</small>
        </div>
      </div>

      <!-- Optional: Remember Me and Forgot Password -->
      <!-- 
      <div class="form-options d-flex justify-content-between align-items-center mb-3">
        <p-checkbox 
          formControlName="rememberMe" 
          label="Remember me"
          binary="true"
          class="remember-checkbox">
        </p-checkbox>
        <a href="#" class="forgot-password text-decoration-none">Forgot password?</a>
      </div> 
      -->

      <p-button
        type="submit"
        label="Sign In"
        [disabled]="loginForm.invalid || isLoading"
        [loading]="isLoading"
        class="w-100 login-button"
        styleClass="p-button-raised"
      ></p-button>
    </form>

    <div class="login-footer text-center mt-3">
      <div class="forgot-password-container">
        <p-button
          type="button"
          label="Forgot password?"
          (click)="onForgotPassword()"
          class="forgot-password p-button-text p-button-plain"
        ></p-button>
      </div>
    </div>
  </div>
</div>
