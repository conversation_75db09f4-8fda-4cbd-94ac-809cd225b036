@use "../../../../shared/SCSS/shared-auth-theme.scss" as *;

.login-card {
  @extend .auth-card;
}

.login-header {
  @extend .auth-header;
}

.login-title {
  @extend .auth-title;
}

.login-subtitle {
  @extend .auth-subtitle;
}

.auth-input-container {
  @extend .auth-input-container;
}

.auth-input-wrapper {
  @extend .auth-input-wrapper;
}

.auth-input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  z-index: 10;
  font-size: 1.1rem;
}

/* Adjust input padding for icon */
.auth-input-wrapper input {
  padding-left: 2.5rem !important;
}

.auth-input-wrapper .p-password {
  width: 100%;
}

.auth-input-wrapper .p-password input {
  padding-left: 2.5rem !important;
}

.auth-error-message {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.auth-error-message i {
  font-size: 0.875rem;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  font-weight: 600;
  border-radius: 8px;
}

.login-footer {
  margin-top: 1.5rem;
}

.forgot-password {
  color: #007bff;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

/* Float label adjustments */
.p-float-label {
  width: 100%;
}

.p-float-label label {
  left: 2.5rem !important; /* Adjust for icon */
}

/* PrimeNG Password component styling */
.p-password .p-inputtext {
  width: 100%;
}

/* Bootstrap form validation styling compatibility */
.is-invalid {
  border-color: #dc3545;
}

.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .login-card {
    margin: 1rem;
    max-width: none;
  }
}
