import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../Services/Auth.service';
import { NotificationService } from '../../../../core/Services/Notification.service';
import { LoginRequest } from '../../models/LoginRequest';
import { LoginResponse } from '../../models/AuthResponse';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  isLoading = false;
  hidePassword = true;

  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);
  private notification = inject(NotificationService);

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
    });
  }

  onSubmit(): void {
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    const loginData: LoginRequest = this.loginForm.value;

    this.authService.login(loginData).subscribe({
      next: (res: LoginResponse) => {
        if (res.data?.requiresOtp) {
          this.notification.showInfo(
            'Please enter the verification code sent to your device.',
          );
          this.router.navigate(['/auth/otp-verification'], {
            queryParams: {
              userId: res.data.userId,
            },
          });
        } else if (res.isSuccess) {
          this.notification.showSuccess('Login successful! Welcome back.');
          this.router.navigate(['/dashboard']);
        }
      },
      error: () => {
        this.isLoading = false;
      },
    });
  }

  onForgotPassword(): void {
    this.router.navigate(['/auth/forgot-password']);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  get emailControl() {
    return this.loginForm.get('email');
  }
  get passwordControl() {
    return this.loginForm.get('password');
  }
}
