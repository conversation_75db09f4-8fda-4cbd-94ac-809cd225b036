import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../Services/Auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NotificationService } from '../../../../core/Services/Notification.service';
import { OtpVerificationResponse } from '../../models/AuthResponse';
import { Subject, Subscription, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-otp-verificaiton',
  standalone: false,
  templateUrl: './otp-verificaiton.component.html',
  styleUrl: './otp-verificaiton.component.scss',
})
export class OtpVerificaitonComponent implements OnInit, OnDestroy {
  otpForm!: FormGroup;
  isLoading: boolean = false;
  userId: string = '';

  // Timer state
  resendTimer = 0;
  canResend = false;
  private timerSubscription?: Subscription;
  destroy$ = new Subject<void>();

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.timerSubscription?.unsubscribe();
  }

  constructor(
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private notification: NotificationService,
  ) {}

  ngOnInit(): void {
    this.otpForm = this.formBuilder.group({
      otp: [
        '',
        [
          Validators.required,
          Validators.pattern(/^\d{5}$/),
          Validators.minLength(5),
          Validators.maxLength(5),
        ],
      ],
    });

    this.route.queryParams.subscribe((params) => {
      this.userId = params['userId'];
      if (!this.userId) {
        this.router.navigate(['/auth/login']);
        return;
      }
      this.startResendTimer();
    });
  }

  onVerifyOtp(): void {
    const otp = this.otpForm.value.otp;

    if (this.otpForm.invalid || otp.length !== 5 || !/^\d{5}$/.test(otp)) {
      this.otpForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    this.authService.loginWith2FA(this.userId, otp).subscribe({
      next: (res: OtpVerificationResponse) => {
        if (res.isSuccess) {
          this.notification.showSuccess(
            'OTP verified successfully! Welcome back.',
          );
          this.router.navigate(['/dashboard']);
        }
      },
      error: () => {
        this.isLoading = false;
        this.clearOtp();
      },
    });
  }

  clearOtp(): void {
    this.otpForm.get('otp')?.setValue('');
    this.otpForm.get('otp')?.markAsUntouched();
  }

  private startResendTimer(): void {
    this.resendTimer = 60; // 60 seconds
    this.canResend = false;

    this.timerSubscription?.unsubscribe();
    this.timerSubscription = interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resendTimer--;
        if (this.resendTimer <= 0) {
          this.canResend = true;
          this.timerSubscription?.unsubscribe();
        }
      });
  }

  get timerDisplay(): string {
    const minutes = Math.floor(this.resendTimer / 60);
    const seconds = this.resendTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
