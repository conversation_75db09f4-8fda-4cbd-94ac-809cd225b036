import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { NotificationService } from '../../../core/Services/Notification.service';

@Injectable({
  providedIn: 'root',
})
export class AuthErrorHandlerService {
  constructor(private notificationService: NotificationService) {}

  public handleError(error: HttpErrorResponse) {
    const apiError = error.error;
    let errorMessage = 'An unexpected error occurred. Please try again.';

    if (error.error instanceof ErrorEvent) {
      errorMessage = error.error.message;
    } else {
      if (error.status === 400) {
        if (apiError?.message) {
          //
          errorMessage = apiError.message;
        } else if (apiError?.errors && typeof apiError.errors === 'object') {
          const errorItems = Object.entries(apiError.errors).map(
            ([key, messages]) => `${key}: ${(messages as string[]).join(', ')}`,
          );
          errorMessage = `Validation Errors: ${errorItems.join('; ')}`;
        } else if (Array.isArray(apiError)) {
          const errorItems = apiError.map(
            (err: { code: string; description: string }) => err.description,
          );
          errorMessage = `Please fix the following issues: ${errorItems.join('; ')}`;
        } else {
          errorMessage = 'Registration failed due to validation issues.';
        }
      } else if (error.status === 401) {
        errorMessage = 'Invalid credentials. Please check email and password.';
      } else if (error.status === 403) {
        // OTP-specific errors
        if (apiError?.message?.toLowerCase().includes('otp')) {
          if (apiError.message.toLowerCase().includes('expired')) {
            errorMessage = 'OTP has expired. Please request a new one.';
          } else if (apiError.message.toLowerCase().includes('invalid')) {
            errorMessage = 'Invalid OTP entered. Please try again.';
          } else if (apiError.message.toLowerCase().includes('missing')) {
            errorMessage = 'OTP is required. Please enter the OTP.';
          } else {
            errorMessage = apiError.message;
          }
        } else {
          errorMessage = 'Access denied. Please check your OTP or permissions.';
        }
      } else {
        errorMessage =
          apiError?.message ||
          `Server error: ${error.status}. Please try again later.`;
      }
    }

    this.notificationService.showError('Authentication Error', errorMessage);

    return throwError(() => apiError?.message || errorMessage);
  }
}
