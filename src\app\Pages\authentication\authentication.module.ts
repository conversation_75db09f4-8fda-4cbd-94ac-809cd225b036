import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthenticationRoutingModule } from './authentication-routing.module';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { LoginComponent } from './Pages/login/login.component';
import { OtpVerificaitonComponent } from './Pages/otp-verificaiton/otp-verificaiton.component';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { ButtonModule } from 'primeng/button';

@NgModule({
  declarations: [LoginComponent, OtpVerificaitonComponent],
  imports: [
    CommonModule,
    AuthenticationRoutingModule,
    ToastModule,
    ReactiveFormsModule,
    ButtonModule,
  ],
  providers: [MessageService],
})
export class AuthenticationModule {}
