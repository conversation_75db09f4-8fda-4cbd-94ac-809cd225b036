import { Injectable } from '@angular/core';
import {
  HttpE<PERSON>,
  HttpInterceptor,
  HttpHandler,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { NotificationService } from '../Services/Notification.service';
import { AuthService } from '../../Pages/authentication/Services/Auth.service';

@Injectable()
export class ServerErrorInterceptor implements HttpInterceptor {
  constructor(
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router,
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        let userMessage = 'An unexpected error occurred. Please try again.';
        switch (error.status) {
          case 401:
            userMessage = 'Your session has expired. Please log in again.';
            if (
              this.authService.isTokenExpired() &&
              this.authService.isLoggedIn()
            ) {
              this.authService.logout();
              this.router.navigate(['/auth/login']);
            }
            break;
          case 403:
            userMessage =
              'Access denied. You do not have permission to perform this action.';
            break;
          case 404:
            userMessage = 'The requested resource was not found.';
            break;
          case 500:
            userMessage =
              'Server error occurred. Please try again later or contact support.';
            break;
          case 0:
            userMessage =
              'Network connection error. Please check your internet connection.';
            break;
        }
        this.notificationService.showError(userMessage);
        return throwError(() => error);
      }),
    );
  }
}
