import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthorizedLayoutComponent } from './Components/authorized-layout/authorized-layout.component';
import { UnauthorizedLayoutComponent } from './Components/unauthorized-layout/unauthorized-layout.component';

@NgModule({
  declarations: [AuthorizedLayoutComponent, UnauthorizedLayoutComponent],
  imports: [CommonModule],
  exports: [AuthorizedLayoutComponent, UnauthorizedLayoutComponent],
})
export class SharedModule {}
